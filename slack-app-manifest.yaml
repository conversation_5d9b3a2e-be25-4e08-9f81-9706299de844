display_information:
  name: <PERSON><PERSON>
  description: A peer-to-peer recognition system that sends Orbstar notifications when employees recognize each other based on company values
  background_color: "#197278"
  long_description: | 
    Stellar is a peer-to-peer recognition system that allows employees to give and receive "Orbstars" - digital recognition tokens based on company values.
    
    This bot automatically sends notifications when:
    - Someone receives an Orbstar recognition
    - Company values are celebrated through peer recognition
    - Recognition milestones are achieved
    
    The bot sends both direct messages to recipients and optional channel notifications to keep the team informed of positive recognition happening across the organisation."
features:
  bot_user:
    display_name: <PERSON><PERSON>
    always_online: false
oauth_config:
  scopes:
    bot:
      - chat:write
      - users:read
      - users:read.email
settings:
  org_deploy_enabled: false
  socket_mode_enabled: false
  token_rotation_enabled: false

# IDP-Initiated Login Support

## Overview

This document describes the IDP-initiated login feature that allows users to start the authentication process from <PERSON><PERSON>'s dashboard while maintaining the existing PKCE security flow.

## Features

- **IDP-Initiated Login**: Users can start login from <PERSON><PERSON>'s dashboard
- **Return URL Support**: Preserves the intended destination after authentication
- **Security**: Validates return URLs to prevent open redirect attacks
- **PKCE Compatibility**: Works seamlessly with existing PKCE authentication flow

## API Endpoints

### IDP-Initiated Login

```http
GET /idp-login?return_to=/dashboard
```

**Parameters:**
- `return_to` (optional): URL to redirect to after successful authentication
  - Must be a relative URL starting with `/` or a same-origin URL
  - Defaults to `/dashboard` if not provided

**Response:**
- `302 Found`: Redirects to `/login` with the return URL preserved

**Security:**
- Validates return URLs to prevent open redirect attacks
- Only allows relative URLs or same-origin URLs

### Enhanced Login Endpoint

```http
GET /login?return_to=/some-page
```

**Parameters:**
- `return_to` (optional): URL to redirect to after successful authentication

**Behavior:**
- Stores the return URL in the PKCE session
- Redirects to Okta for authentication
- Return URL is preserved through the OAuth flow

## Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Okta
    participant Backend
    participant Frontend

    User->>Okta: Click app in Okta dashboard
    Okta->>Backend: GET /idp-login?return_to=/target-page
    Backend->>Backend: Validate return URL
    Backend->>Backend: Redirect to /login with return_to
    Backend->>Backend: Store return_to in PKCE session
    Backend->>Okta: Redirect to OAuth authorization
    Okta->>User: Show login form (if needed)
    User->>Okta: Authenticate
    Okta->>Backend: GET /login/callback?code=...&state=...
    Backend->>Backend: Retrieve return_to from session
    Backend->>Okta: Exchange code for tokens
    Backend->>Backend: Generate JWT tokens
    Backend->>Backend: Store tokens in temporary session
    Backend->>Frontend: Redirect with session ID and return_to
    Frontend->>Backend: POST /auth/exchange with session ID
    Backend->>Frontend: Return JWT tokens
    Frontend->>Frontend: Store tokens and redirect to return_to
```

## Configuration

### Okta Application Settings

To enable IDP-initiated login in Okta:

1. **Navigate to your Okta application settings**
2. **Set "Initiate login URI"** to: `http://localhost:8080/idp-login`
   - For production: `https://your-domain.com/idp-login`
3. **Set "Login initiated by"** to: "Either Okta or App"
4. **Choose "Redirect to app to initiate login (OIDC Compliant)"**

### Environment Variables

No additional environment variables are required. The feature uses existing configuration:

```env
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8080
```

## Security Considerations

### URL Validation

The `isValidReturnURL` function validates return URLs to prevent open redirect attacks:

- **Relative URLs**: Allows URLs starting with `/` (but not `//`)
- **Same-origin URLs**: Allows URLs with the same scheme and host as `FRONTEND_URL`
- **Blocked**: External URLs, protocol-relative URLs, and malformed URLs

### Session Security

- Return URLs are stored in PKCE sessions with automatic cleanup
- Sessions expire after 10 minutes
- State parameter validation prevents CSRF attacks

## Testing

### Manual Testing

1. **SP-Initiated Flow** (existing):
   ```bash
   curl -I http://localhost:8080/login
   ```

2. **IDP-Initiated Flow** (new):
   ```bash
   curl -I "http://localhost:8080/idp-login?return_to=/dashboard"
   ```

3. **Invalid Return URL**:
   ```bash
   curl -I "http://localhost:8080/idp-login?return_to=https://evil.com"
   ```

### Automated Tests

The implementation includes comprehensive tests:

- `TestAuthHandler_HandleIDPInitiatedLogin_Success`
- `TestAuthHandler_HandleIDPInitiatedLogin_DefaultReturnTo`
- `TestAuthHandler_HandleIDPInitiatedLogin_InvalidReturnTo`

Run tests with:
```bash
go test ./internal/api/handlers/... -v
```

## Integration Examples

### Frontend Handling

When the frontend receives the redirect with `return_to` parameter:

```javascript
// Extract parameters from URL
const urlParams = new URLSearchParams(window.location.search);
const sessionId = urlParams.get('session');
const returnTo = urlParams.get('return_to') || '/dashboard';

// Exchange session for tokens
const response = await fetch('/auth/exchange', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ session_id: sessionId })
});

const { access, refresh } = await response.json();

// Store tokens
localStorage.setItem('access_token', access);
localStorage.setItem('refresh_token', refresh);

// Redirect to intended destination
window.location.href = returnTo;
```

### Error Handling

```javascript
try {
  // Handle authentication...
} catch (error) {
  if (error.code === 'invalid_return_url') {
    // Redirect to default page
    window.location.href = '/dashboard';
  }
}
```

## Troubleshooting

### Common Issues

1. **"Invalid return URL" error**:
   - Check that the return URL is relative or same-origin
   - Verify `FRONTEND_URL` configuration

2. **Return URL not preserved**:
   - Ensure the state parameter is being passed correctly
   - Check PKCE session storage and cleanup

3. **Okta configuration issues**:
   - Verify "Initiate login URI" is set correctly
   - Ensure "Login initiated by" allows "Either Okta or App"

### Debug Logging

Enable debug logging to troubleshoot issues:

```go
log.Printf("Return URL: %s", returnTo)
log.Printf("State: %s", state)
log.Printf("Stored return URL: %s", storedReturnURL)
```

## Migration Notes

This feature is backward compatible:

- Existing SP-initiated flows continue to work unchanged
- No database migrations required
- No breaking changes to existing APIs
- Frontend changes are optional (return_to parameter is handled gracefully)

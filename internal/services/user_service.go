package services

import (
	"fmt"
	"strings"

	"stellar-go/internal/models"

	"gorm.io/gorm"
)

// UserService handles user-related business logic
type UserService struct {
	db           *gorm.DB
	tokenService *TokenService
}

// NewUserService creates a new user service
func NewUserService(db *gorm.DB) *UserService {
	tokenService := NewTokenService(db)
	return &UserService{
		db:           db,
		tokenService: tokenService,
	}
}

// GetUserByID retrieves a user by ID
func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	err := s.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByEmail retrieves a user by email
func (s *UserService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	err := s.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUsers retrieves users with optional filtering
func (s *UserService) GetUsers(filter *models.UserListFilter) ([]models.User, error) {
	query := s.db.Where("show = ?", true)

	// Apply filters
	if filter.FirstName != "" {
		query = query.Where("first_name ILIKE ?", "%"+filter.FirstName+"%")
	}
	if filter.LastName != "" {
		query = query.Where("last_name ILIKE ?", "%"+filter.LastName+"%")
	}
	if filter.Email != "" {
		query = query.Where("email ILIKE ?", "%"+filter.Email+"%")
	}

	var users []models.User
	err := query.Order("first_name ASC, last_name ASC").Find(&users).Error
	return users, err
}

// GetUsersForSCIM retrieves users for SCIM with pagination and optional filtering
func (s *UserService) GetUsersForSCIM(offset, limit int, emailFilter string) ([]models.User, int, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{})

	// Apply email filter if provided (for SCIM userName eq filter)
	if emailFilter != "" {
		query = query.Where("email = ?", emailFilter)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	err := query.Offset(offset).Limit(limit).Order("id ASC").Find(&users).Error
	return users, int(total), err
}

// GetUserCount returns the total number of users
func (s *UserService) GetUserCount() (int64, error) {
	var count int64
	err := s.db.Model(&models.User{}).Count(&count).Error
	return count, err
}

// CreateUser creates a new user
func (s *UserService) CreateUser(user *models.User) (*models.User, error) {
	// Validate email uniqueness
	var existingUser models.User
	err := s.db.Where("email = ?", user.Email).First(&existingUser).Error
	if err == nil {
		return nil, fmt.Errorf("user with email %s already exists", user.Email)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check email uniqueness: %w", err)
	}

	// Create the user
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// UpdateUser updates an existing user
func (s *UserService) UpdateUser(user *models.User) (*models.User, error) {
	if err := s.db.Save(user).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}
	return user, nil
}

// DeleteUser deletes a user by ID
func (s *UserService) DeleteUser(id uint) error {
	// Check if user exists
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		return err
	}

	// Delete the user
	if err := s.db.Delete(&user).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// CreateOrUpdateUserFromOkta creates or updates a user from Okta data
func (s *UserService) CreateOrUpdateUserFromOkta(email, firstName, lastName string, isAdmin bool) (*models.User, error) {
	var user models.User
	
	// Try to find existing user
	err := s.db.Where("email = ?", email).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		// Create new user
		user = models.User{
			Email:     email,
			FirstName: firstName,
			LastName:  lastName,
			Show:      true, // Default to visible for Okta users
			Active:    true, // Default to active for Okta users
			Admin:     isAdmin,
		}
		if err := s.db.Create(&user).Error; err != nil {
			return nil, fmt.Errorf("failed to create user from Okta: %w", err)
		}
	} else if err != nil {
		return nil, fmt.Errorf("failed to query user: %w", err)
	} else {
		// Update existing user
		user.FirstName = firstName
		user.LastName = lastName
		user.Admin = isAdmin
		if err := s.db.Save(&user).Error; err != nil {
			return nil, fmt.Errorf("failed to update user from Okta: %w", err)
		}
	}

	return &user, nil
}

// ValidateUserExists checks if a user exists by ID
func (s *UserService) ValidateUserExists(id uint) error {
	var count int64
	err := s.db.Model(&models.User{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to validate user existence: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("user with id %d does not exist", id)
	}
	return nil
}

// ValidateUsersExist checks if multiple users exist by their IDs
func (s *UserService) ValidateUsersExist(ids []uint) error {
	var count int64
	err := s.db.Model(&models.User{}).Where("id IN ?", ids).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to validate users existence: %w", err)
	}
	if int(count) != len(ids) {
		return fmt.Errorf("one or more users do not exist")
	}
	return nil
}

// GetUsersByIDs retrieves multiple users by their IDs
func (s *UserService) GetUsersByIDs(ids []uint) ([]models.User, error) {
	var users []models.User
	err := s.db.Where("id IN ?", ids).Find(&users).Error
	return users, err
}

// SearchUsers searches for users by name or email
func (s *UserService) SearchUsers(query string) ([]models.User, error) {
	if query == "" {
		return s.GetUsers(&models.UserListFilter{})
	}

	// Search in first name, last name, and email
	searchPattern := "%" + strings.ToLower(query) + "%"
	var users []models.User
	
	err := s.db.Where("show = ? AND (LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ?)",
		true, searchPattern, searchPattern, searchPattern).
		Order("first_name ASC, last_name ASC").
		Find(&users).Error
	
	return users, err
}

// DeactivateUser deactivates a user and invalidates their tokens
func (s *UserService) DeactivateUser(userID uint) error {
	// Update user active status
	err := s.db.Model(&models.User{}).Where("id = ?", userID).Update("active", false).Error
	if err != nil {
		return fmt.Errorf("failed to deactivate user: %w", err)
	}

	// Note: We don't need to blacklist existing tokens because the middleware
	// will check the user's active status on each request
	return nil
}

// ActivateUser activates a user
func (s *UserService) ActivateUser(userID uint) error {
	err := s.db.Model(&models.User{}).Where("id = ?", userID).Update("active", true).Error
	if err != nil {
		return fmt.Errorf("failed to activate user: %w", err)
	}
	return nil
}

// IsUserActive checks if a user is active for authentication
func (s *UserService) IsUserActive(userID uint) (bool, error) {
	var user models.User
	err := s.db.Select("active").First(&user, userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, fmt.Errorf("user not found")
		}
		return false, fmt.Errorf("failed to check user active status: %w", err)
	}
	return user.Active, nil
}

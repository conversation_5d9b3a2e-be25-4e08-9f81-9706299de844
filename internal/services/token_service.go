package services

import (
	"fmt"
	"time"

	"stellar-go/internal/models"

	"gorm.io/gorm"
)

// TokenService handles token-related business logic
type TokenService struct {
	db *gorm.DB
}

// NewTokenService creates a new token service
func NewTokenService(db *gorm.DB) *TokenService {
	service := &TokenService{db: db}

	// Start background cleanup goroutine
	go service.startCleanupRoutine()

	return service
}

// BlacklistRefreshToken adds a refresh token to the blacklist
func (s *TokenService) BlacklistRefreshToken(tokenID string, userID uint, expiresAt time.Time) error {
	blacklistEntry := models.RefreshTokenBlacklist{
		TokenID:   tokenID,
		UserID:    userID,
		ExpiresAt: expiresAt,
	}

	err := s.db.Create(&blacklistEntry).Error
	if err != nil {
		return fmt.Errorf("failed to blacklist refresh token: %w", err)
	}

	return nil
}

// IsRefreshTokenBlacklisted checks if a refresh token is blacklisted
func (s *TokenService) IsRefreshTokenBlacklisted(tokenID string) (bool, error) {
	var count int64
	err := s.db.Model(&models.RefreshTokenBlacklist{}).
		Where("token_id = ? AND expires_at > ?", tokenID, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check token blacklist: %w", err)
	}

	return count > 0, nil
}

// BlacklistAllUserTokens blacklists all refresh tokens for a specific user
func (s *TokenService) BlacklistAllUserTokens(userID uint) error {
	// We can't blacklist tokens we don't know about, but we can mark the user as inactive
	// and the middleware will handle rejecting tokens for inactive users
	// This method is here for future use if we decide to track active tokens
	return nil
}

// CleanupExpiredBlacklistEntries removes expired blacklist entries
func (s *TokenService) CleanupExpiredBlacklistEntries() error {
	err := s.db.Where("expires_at < ?", time.Now()).Delete(&models.RefreshTokenBlacklist{}).Error
	if err != nil {
		return fmt.Errorf("failed to cleanup expired blacklist entries: %w", err)
	}

	return nil
}

// GetBlacklistedTokensForUser returns all blacklisted tokens for a user
func (s *TokenService) GetBlacklistedTokensForUser(userID uint) ([]models.RefreshTokenBlacklist, error) {
	var tokens []models.RefreshTokenBlacklist
	err := s.db.Where("user_id = ? AND expires_at > ?", userID, time.Now()).
		Order("blacklisted_at DESC").
		Find(&tokens).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get blacklisted tokens for user: %w", err)
	}

	return tokens, nil
}

// startCleanupRoutine starts a background goroutine to clean up expired blacklist entries
func (s *TokenService) startCleanupRoutine() {
	ticker := time.NewTicker(1 * time.Hour) // Clean up every hour
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			err := s.CleanupExpiredBlacklistEntries()
			if err != nil {
				// Log error but don't stop the routine
				// In a production system, you'd use a proper logger here
				fmt.Printf("Error cleaning up expired blacklist entries: %v\n", err)
			}
		}
	}
}

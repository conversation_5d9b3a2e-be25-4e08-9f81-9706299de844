package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUser_ToSummary(t *testing.T) {
	user := User{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
		Active:    true,
		Admin:     false,
	}

	summary := user.ToSummary()

	assert.Equal(t, "<EMAIL>", summary.Email)
	assert.Equal(t, "Test", summary.FirstName)
	assert.Equal(t, "User", summary.LastName)
}

func TestUser_GetFullName(t *testing.T) {
	user := User{
		FirstName: "John",
		LastName:  "Doe",
	}

	fullName := user.GetFullName()
	assert.Equal(t, "<PERSON> Doe", fullName)
}

func TestUser_IsActive(t *testing.T) {
	tests := []struct {
		name     string
		active   bool
		expected bool
	}{
		{"active user", true, true},
		{"inactive user", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user := User{Active: tt.active}
			assert.Equal(t, tt.expected, user.IsActive())
		})
	}
}

func TestUser_IsAdmin(t *testing.T) {
	tests := []struct {
		name     string
		admin    bool
		expected bool
	}{
		{"admin user", true, true},
		{"regular user", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user := User{Admin: tt.admin}
			assert.Equal(t, tt.expected, user.IsAdmin())
		})
	}
}

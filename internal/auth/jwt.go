package auth

import (
	"errors"
	"fmt"
	"time"

	"stellar-go/internal/config"

	"github.com/golang-jwt/jwt/v5"
)

// TokenType represents the type of JWT token
type TokenType string

const (
	AccessToken  TokenType = "access"
	RefreshToken TokenType = "refresh"
)

// Claims represents the JWT claims
type Claims struct {
	UserID uint      `json:"sub"`
	Type   TokenType `json:"typ"`
	jwt.RegisteredClaims
}

// JWTManager handles JWT token operations
type JWTManager struct {
	secretKey   []byte
	backendURL  string
	frontendURL string
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(cfg *config.Config) *JWTManager {
	return &JWTManager{
		secretKey:   []byte(cfg.JWT.SecretKey),
		backendURL:  cfg.JWT.BackendURL,
		frontendURL: cfg.JWT.FrontendURL,
	}
}

// GenerateTokenPair generates both access and refresh tokens
func (j *J<PERSON>TManager) GenerateTokenPair(userID uint) (string, string, error) {
	accessToken, err := j.generateToken(userID, AccessToken, 2*time.Minute)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := j.generateToken(userID, RefreshToken, 2*time.Hour)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return accessToken, refreshToken, nil
}

// generateToken generates a JWT token with the specified parameters
func (j *JWTManager) generateToken(userID uint, tokenType TokenType, duration time.Duration) (string, error) {
	now := time.Now()
	claims := Claims{
		UserID: userID,
		Type:   tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        fmt.Sprintf("%d-%d", userID, now.Unix()),
			Issuer:    j.backendURL,
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{j.frontendURL},
			ExpiresAt: jwt.NewNumericDate(now.Add(duration)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	return token.SignedString(j.secretKey)
}

// ValidateToken validates a JWT token and returns the claims
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Validate issuer
	if claims.Issuer != j.backendURL {
		return nil, errors.New("invalid token issuer")
	}

	// Validate audience
	if len(claims.Audience) == 0 || claims.Audience[0] != j.frontendURL {
		return nil, errors.New("invalid token audience")
	}

	return claims, nil
}

// ValidateAccessToken validates an access token specifically
func (j *JWTManager) ValidateAccessToken(tokenString string) (*Claims, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.Type != AccessToken {
		return nil, errors.New("token is not an access token")
	}

	return claims, nil
}

// ValidateRefreshToken validates a refresh token specifically
func (j *JWTManager) ValidateRefreshToken(tokenString string) (*Claims, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.Type != RefreshToken {
		return nil, errors.New("token is not a refresh token")
	}

	return claims, nil
}

// RefreshTokens validates a refresh token and generates new token pair
func (j *JWTManager) RefreshTokens(accessTokenString, refreshTokenString string) (string, string, error) {
	// Validate refresh token
	refreshClaims, err := j.ValidateRefreshToken(refreshTokenString)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// Parse access token without validation (it might be expired)
	accessToken, _ := jwt.ParseWithClaims(accessTokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return j.secretKey, nil
	})

	if accessToken == nil {
		return "", "", errors.New("invalid access token format")
	}

	accessClaims, ok := accessToken.Claims.(*Claims)
	if !ok {
		return "", "", errors.New("invalid access token claims")
	}

	// Verify that the tokens belong to the same user
	if accessClaims.UserID != refreshClaims.UserID {
		return "", "", errors.New("token mismatch")
	}

	// Generate new token pair
	return j.GenerateTokenPair(refreshClaims.UserID)
}

// RefreshTokensWithBlacklistCheck validates a refresh token, checks blacklist, and generates new token pair
func (j *JWTManager) RefreshTokensWithBlacklistCheck(accessTokenString, refreshTokenString string, tokenService interface{}) (string, string, error) {
	// First validate the refresh token
	refreshClaims, err := j.ValidateRefreshToken(refreshTokenString)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// Check if refresh token is blacklisted (if tokenService is provided)
	if ts, ok := tokenService.(interface {
		IsRefreshTokenBlacklisted(string) (bool, error)
	}); ok {
		isBlacklisted, err := ts.IsRefreshTokenBlacklisted(refreshClaims.ID)
		if err != nil {
			return "", "", fmt.Errorf("failed to check token blacklist: %w", err)
		}
		if isBlacklisted {
			return "", "", errors.New("refresh token has been invalidated")
		}
	}

	// Parse access token without validation (it might be expired)
	accessToken, _ := jwt.ParseWithClaims(accessTokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return j.secretKey, nil
	})

	if accessToken == nil {
		return "", "", errors.New("invalid access token format")
	}

	accessClaims, ok := accessToken.Claims.(*Claims)
	if !ok {
		return "", "", errors.New("invalid access token claims")
	}

	// Verify that both tokens belong to the same user
	if accessClaims.UserID != refreshClaims.UserID {
		return "", "", errors.New("token user mismatch")
	}

	// Generate new token pair
	return j.GenerateTokenPair(refreshClaims.UserID)
}

// ExtractTokenID extracts the token ID from a JWT token string
func (j *JWTManager) ExtractTokenID(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		// Try to parse without validation to get the ID even if token is expired
		token, _ := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
			return j.secretKey, nil
		})

		if token != nil {
			if claims, ok := token.Claims.(*Claims); ok {
				return claims.ID, nil
			}
		}
		return "", fmt.Errorf("failed to extract token ID: %w", err)
	}

	return claims.ID, nil
}

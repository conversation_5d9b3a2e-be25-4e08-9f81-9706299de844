package auth

import (
	"testing"
	"time"

	"stellar-go/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOktaManager_PKCEFlow(t *testing.T) {
	cfg := &config.Config{
		Okta: config.OktaConfig{
			Domain:       "test.okta.com",
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			RedirectURI:  "http://localhost:8080/login/callback",
		},
	}

	manager, err := NewOktaManager(cfg)
	require.NoError(t, err)
	require.NotNil(t, manager)

	// Test GenerateAuthURL with PKCE
	authURL, state, nonce, err := manager.GenerateAuthURL()
	require.NoError(t, err)
	assert.NotEmpty(t, authURL)
	assert.NotEmpty(t, state)
	assert.NotEmpty(t, nonce)

	// Verify PKCE parameters are in the URL
	assert.Contains(t, authURL, "code_challenge=")
	assert.Contains(t, authURL, "code_challenge_method=S256")

	// Verify PKCE session is stored
	manager.sessionMutex.RLock()
	pkceSession, exists := manager.pkceSessions[state]
	manager.sessionMutex.RUnlock()

	assert.True(t, exists)
	assert.NotNil(t, pkceSession)
	assert.NotEmpty(t, pkceSession.CodeVerifier)
	assert.Equal(t, state, pkceSession.State)
	assert.Equal(t, nonce, pkceSession.Nonce)
}

func TestOktaManager_TokenSessionManagement(t *testing.T) {
	cfg := &config.Config{
		Okta: config.OktaConfig{
			Domain:       "test.okta.com",
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			RedirectURI:  "http://localhost:8080/login/callback",
		},
	}

	manager, err := NewOktaManager(cfg)
	require.NoError(t, err)

	// Test storing token session
	accessToken := "test-access-token"
	refreshToken := "test-refresh-token"

	sessionID, err := manager.StoreTokenSession(accessToken, refreshToken)
	require.NoError(t, err)
	assert.NotEmpty(t, sessionID)

	// Test exchanging token session
	retrievedAccess, retrievedRefresh, err := manager.ExchangeTokenSession(sessionID)
	require.NoError(t, err)
	assert.Equal(t, accessToken, retrievedAccess)
	assert.Equal(t, refreshToken, retrievedRefresh)

	// Test that session is deleted after exchange (one-time use)
	_, _, err = manager.ExchangeTokenSession(sessionID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid or expired session")
}

func TestOktaManager_SessionExpiration(t *testing.T) {
	cfg := &config.Config{
		Okta: config.OktaConfig{
			Domain:       "test.okta.com",
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			RedirectURI:  "http://localhost:8080/login/callback",
		},
	}

	manager, err := NewOktaManager(cfg)
	require.NoError(t, err)

	// Store a token session
	sessionID, err := manager.StoreTokenSession("access", "refresh")
	require.NoError(t, err)

	// Manually set the session creation time to past expiration
	manager.sessionMutex.Lock()
	if session, exists := manager.tokenSessions[sessionID]; exists {
		session.CreatedAt = time.Now().Add(-6 * time.Minute) // 6 minutes ago (expired)
	}
	manager.sessionMutex.Unlock()

	// Try to exchange expired session
	_, _, err = manager.ExchangeTokenSession(sessionID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "session expired")
}

func TestGeneratePKCECodeVerifier(t *testing.T) {
	verifier, err := generatePKCECodeVerifier()
	require.NoError(t, err)
	assert.NotEmpty(t, verifier)
	
	// PKCE code verifier should be 43-128 characters
	assert.GreaterOrEqual(t, len(verifier), 43)
	assert.LessOrEqual(t, len(verifier), 128)
}

func TestGeneratePKCECodeChallenge(t *testing.T) {
	verifier := "test-code-verifier-12345"
	challenge := generatePKCECodeChallenge(verifier)
	
	assert.NotEmpty(t, challenge)
	assert.NotEqual(t, verifier, challenge)
	
	// Should be consistent for the same verifier
	challenge2 := generatePKCECodeChallenge(verifier)
	assert.Equal(t, challenge, challenge2)
}

package auth

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"stellar-go/internal/config"
)

// PKCESession represents a PKCE session for temporary storage
type PKCESession struct {
	CodeVerifier string
	State        string
	Nonce        string
	ReturnURL    string    // URL to redirect to after successful authentication
	CreatedAt    time.Time
}

// SessionData represents temporary session data for token exchange
type SessionData struct {
	AccessToken  string
	RefreshToken string
	CreatedAt    time.Time
}

// OktaManager handles Okta OIDC integration (no Management API)
type OktaManager struct {
	config         *config.OktaConfig
	redirectURI    string
	httpClient     *http.Client
	pkceSessions   map[string]*PKCESession // state -> PKCESession
	tokenSessions  map[string]*SessionData // sessionID -> SessionData
	sessionMutex   sync.RWMutex
}

// OktaUserInfo represents user information from Okta
type OktaUserInfo struct {
	ID        string
	Email     string
	FirstName string
	LastName  string
	IsAdmin   bool
}

// NewOktaManager creates a new Okta manager for OIDC authentication only
func NewOktaManager(cfg *config.Config) (*OktaManager, error) {
	manager := &OktaManager{
		config:        &cfg.Okta,
		redirectURI:   cfg.Okta.RedirectURI,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		pkceSessions:  make(map[string]*PKCESession),
		tokenSessions: make(map[string]*SessionData),
	}

	// Start cleanup goroutine for expired sessions
	go manager.cleanupExpiredSessions()

	return manager, nil
}

// GenerateAuthURL generates the Okta authorization URL with PKCE support
func (o *OktaManager) GenerateAuthURL() (string, string, string, error) {
	// Generate random state and nonce
	state, err := generateRandomString(32)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to generate state: %w", err)
	}

	nonce, err := generateRandomString(32)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Generate PKCE code_verifier (43-128 characters)
	codeVerifier, err := generatePKCECodeVerifier()
	if err != nil {
		return "", "", "", fmt.Errorf("failed to generate code verifier: %w", err)
	}

	// Generate PKCE code_challenge (SHA256 hash of verifier, base64url encoded)
	codeChallenge := generatePKCECodeChallenge(codeVerifier)

	// Store PKCE session
	o.sessionMutex.Lock()
	o.pkceSessions[state] = &PKCESession{
		CodeVerifier: codeVerifier,
		State:        state,
		Nonce:        nonce,
		CreatedAt:    time.Now(),
	}
	o.sessionMutex.Unlock()

	// Build authorization URL with PKCE parameters
	baseURL := fmt.Sprintf("https://%s/oauth2/default/v1/authorize", o.config.Domain)
	params := url.Values{
		"client_id":             {o.config.ClientID},
		"response_type":         {"code"},
		"scope":                 {"openid profile email"},
		"redirect_uri":          {o.redirectURI},
		"state":                 {state},
		"nonce":                 {nonce},
		"code_challenge":        {codeChallenge},
		"code_challenge_method": {"S256"},
	}

	authURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	return authURL, state, nonce, nil
}

// ExchangeCodeForTokens exchanges authorization code for tokens using PKCE
func (o *OktaManager) ExchangeCodeForTokens(code, state string) (*OktaTokenResponse, error) {
	// Retrieve and validate PKCE session
	o.sessionMutex.RLock()
	pkceSession, exists := o.pkceSessions[state]
	o.sessionMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("invalid or expired PKCE session")
	}

	// Clean up PKCE session after use
	defer func() {
		o.sessionMutex.Lock()
		delete(o.pkceSessions, state)
		o.sessionMutex.Unlock()
	}()

	tokenURL := fmt.Sprintf("https://%s/oauth2/default/v1/token", o.config.Domain)

	data := url.Values{
		"grant_type":    {"authorization_code"},
		"client_id":     {o.config.ClientID},
		"client_secret": {o.config.ClientSecret},
		"code":          {code},
		"redirect_uri":  {o.redirectURI},
		"code_verifier": {pkceSession.CodeVerifier},
	}

	req, err := http.NewRequest("POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create token request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	resp, err := o.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for tokens: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("token exchange failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResponse OktaTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	return &tokenResponse, nil
}

// GetUserInfo retrieves user information from Okta's userinfo endpoint
func (o *OktaManager) GetUserInfo(accessToken string) (*OktaUserInfo, error) {
	userInfoURL := fmt.Sprintf("https://%s/oauth2/default/v1/userinfo", o.config.Domain)

	req, err := http.NewRequest("GET", userInfoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create userinfo request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Accept", "application/json")

	resp, err := o.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("userinfo request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var userInfo struct {
		Sub               string   `json:"sub"`
		Email             string   `json:"email"`
		GivenName         string   `json:"given_name"`
		FamilyName        string   `json:"family_name"`
		Groups            []string `json:"groups"`
		PreferredUsername string   `json:"preferred_username"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode userinfo response: %w", err)
	}

	// Check if user is admin based on group membership
	isAdmin := false
	if o.config.AdminGroup != "" {
		for _, group := range userInfo.Groups {
			if group == o.config.AdminGroup {
				isAdmin = true
				break
			}
		}
	}

	return &OktaUserInfo{
		ID:        userInfo.Sub,
		Email:     userInfo.Email,
		FirstName: userInfo.GivenName,
		LastName:  userInfo.FamilyName,
		IsAdmin:   isAdmin,
	}, nil
}



// OktaTokenResponse represents the token response from Okta OIDC
type OktaTokenResponse struct {
	AccessToken  string `json:"access_token"`
	IDToken      string `json:"id_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// StoreTokenSession stores JWT tokens in a temporary session
func (o *OktaManager) StoreTokenSession(accessToken, refreshToken string) (string, error) {
	sessionID, err := generateRandomString(32)
	if err != nil {
		return "", fmt.Errorf("failed to generate session ID: %w", err)
	}

	o.sessionMutex.Lock()
	o.tokenSessions[sessionID] = &SessionData{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		CreatedAt:    time.Now(),
	}
	o.sessionMutex.Unlock()

	return sessionID, nil
}

// ExchangeTokenSession retrieves and deletes JWT tokens from session
func (o *OktaManager) ExchangeTokenSession(sessionID string) (string, string, error) {
	o.sessionMutex.Lock()
	defer o.sessionMutex.Unlock()

	session, exists := o.tokenSessions[sessionID]
	if !exists {
		return "", "", fmt.Errorf("invalid or expired session")
	}

	// Check if session is expired (5 minutes)
	if time.Since(session.CreatedAt) > 5*time.Minute {
		delete(o.tokenSessions, sessionID)
		return "", "", fmt.Errorf("session expired")
	}

	// Get tokens and delete session (one-time use)
	accessToken := session.AccessToken
	refreshToken := session.RefreshToken
	delete(o.tokenSessions, sessionID)

	return accessToken, refreshToken, nil
}

// cleanupExpiredSessions runs periodically to clean up expired sessions
func (o *OktaManager) cleanupExpiredSessions() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		o.sessionMutex.Lock()
		now := time.Now()

		// Clean up expired PKCE sessions (10 minutes)
		for state, session := range o.pkceSessions {
			if now.Sub(session.CreatedAt) > 10*time.Minute {
				delete(o.pkceSessions, state)
			}
		}

		// Clean up expired token sessions (5 minutes)
		for sessionID, session := range o.tokenSessions {
			if now.Sub(session.CreatedAt) > 5*time.Minute {
				delete(o.tokenSessions, sessionID)
			}
		}

		o.sessionMutex.Unlock()
	}
}

// generateRandomString generates a cryptographically secure random string
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length], nil
}

// generatePKCECodeVerifier generates a PKCE code_verifier (43-128 characters)
func generatePKCECodeVerifier() (string, error) {
	// Generate 32 random bytes (will result in 43 characters when base64url encoded)
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(bytes), nil
}

// generatePKCECodeChallenge generates a PKCE code_challenge from code_verifier
func generatePKCECodeChallenge(codeVerifier string) string {
	hash := sha256.Sum256([]byte(codeVerifier))
	return base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(hash[:])
}

// ValidateState validates the state parameter for CSRF protection
func ValidateState(receivedState, expectedState string) bool {
	return receivedState == expectedState && receivedState != ""
}

// ParseEmailDomain extracts the domain from an email address
func ParseEmailDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return parts[1]
}

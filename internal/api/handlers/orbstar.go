package handlers

import (
	"net/http"
	"strconv"

	"stellar-go/internal/middleware"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OrbstarHandler handles orbstar-related endpoints
type OrbstarHandler struct {
	orbstarService *services.OrbstarService
	userService    *services.UserService
}

// NewOrbstarHandler creates a new orbstar handler
func NewOrbstarHandler(orbstarService *services.OrbstarService, userService *services.UserService) *OrbstarHandler {
	return &OrbstarHandler{
		orbstarService: orbstarService,
		userService:    userService,
	}
}

// GetOrbstars handles GET /orbstars
// @Summary List all orbstars
// @Description Retrieve a paginated list of all orbstars in the system
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Success 200 {object} models.PaginatedOrbstars
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orbstars [get]
func (h *OrbstarHandler) GetOrbstars(c *gin.Context) {
	var params models.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid query parameters",
			Code:  "invalid_params",
		})
		return
	}

	page := params.GetPage()
	orbstars, err := h.orbstarService.GetOrbstars(page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to retrieve orbstars",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, orbstars)
}

// CreateOrbstars handles POST /orbstars
// @Summary Create new orbstar(s)
// @Description Give recognition by creating one or more orbstars. You can recognize up to 5 people in a single request.
// @Description Each recipient will receive a separate orbstar with the same value and description.
// @Description
// @Description **Request Examples:**
// @Description
// @Description Single recipient:
// @Description ```json
// @Description {
// @Description   "receiver": 123,
// @Description   "description": "Outstanding work on the quarterly presentation",
// @Description   "value": "Impact"
// @Description }
// @Description ```
// @Description
// @Description Multiple recipients:
// @Description ```json
// @Description {
// @Description   "receiver": [123, 456, 789],
// @Description   "description": "Excellent teamwork during the product launch",
// @Description   "value": "Initiative"
// @Description }
// @Description ```
// @Description
// @Description **Available Values:** "Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability"
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param orbstar body models.CreateOrbstarRequest true "Orbstar creation data"
// @Success 201 {array} models.OrbstarResponse "Successfully created orbstar(s)"
// @Failure 400 {object} models.ErrorResponse "Bad request - validation errors"
// @Failure 401 {object} models.ErrorResponse "Unauthorized - authentication required"
// @Router /orbstars [post]
func (h *OrbstarHandler) CreateOrbstars(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	var req models.CreateOrbstarRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	// Validate request
	if req.Description == "" {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse{
			Errors: map[string]interface{}{
				"description": []string{"This field is required"},
			},
		})
		return
	}

	if !req.Value.IsValid() {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse{
			Errors: map[string]interface{}{
				"value": []string{"Invalid company value"},
			},
		})
		return
	}

	// Create orbstars
	orbstars, err := h.orbstarService.CreateOrbstars(userID, &req)
	if err != nil {
		// Handle specific validation errors
		if err.Error() == "receiver may not be the same as the user making the request" {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error: err.Error(),
				Code:  "self_recognition",
			})
			return
		}

		// Handle receiver validation errors
		if err.Error() == "one or more users do not exist" {
			c.JSON(http.StatusBadRequest, models.ValidationErrorResponse{
				Errors: map[string]interface{}{
					"receiver": []string{"One or more receivers do not exist"},
				},
			})
			return
		}

		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Failed to create orbstars",
			Code:  "creation_error",
		})
		return
	}

	c.JSON(http.StatusCreated, orbstars)
}

// DeleteOrbstar handles DELETE /orbstars
// @Summary Delete an orbstar (Admin only)
// @Description Delete a specific orbstar. Only administrators can perform this action.
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param orbstar_id query int true "ID of the orbstar to delete"
// @Success 204 "No Content"
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /orbstars [delete]
func (h *OrbstarHandler) DeleteOrbstar(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	// Check if user is admin
	user, err := h.userService.GetUserByID(userID)
	if err != nil || !user.Admin {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Admin privileges required",
			Code:  "admin_required",
		})
		return
	}

	// Get orbstar ID from query parameter
	orbstarIDStr := c.Query("orbstar_id")
	if orbstarIDStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "orbstar_id parameter is required",
			Code:  "missing_orbstar_id",
		})
		return
	}

	orbstarID, err := strconv.ParseUint(orbstarIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid orbstar ID",
			Code:  "invalid_orbstar_id",
		})
		return
	}

	// Delete orbstar
	err = h.orbstarService.DeleteOrbstar(uint(orbstarID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: "Orbstar not found",
				Code:  "orbstar_not_found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: "Failed to delete orbstar",
				Code:  "internal_error",
			})
		}
		return
	}

	c.Status(http.StatusNoContent)
}

// GetReceivedOrbstars handles GET /orbstars/received
// @Summary Get orbstars received by current user
// @Description Retrieve all orbstars received by the authenticated user, along with value statistics
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Success 200 {object} models.UserOrbstarsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orbstars/received [get]
func (h *OrbstarHandler) GetReceivedOrbstars(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	var params models.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid query parameters",
			Code:  "invalid_params",
		})
		return
	}

	page := params.GetPage()
	response, err := h.orbstarService.GetUserReceivedOrbstars(userID, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to retrieve received orbstars",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetGivenOrbstars handles GET /orbstars/given
// @Summary Get orbstars given by current user
// @Description Retrieve all orbstars given by the authenticated user, along with value statistics
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number for pagination" default(1) minimum(1)
// @Success 200 {object} models.UserOrbstarsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orbstars/given [get]
func (h *OrbstarHandler) GetGivenOrbstars(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	var params models.PaginationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid query parameters",
			Code:  "invalid_params",
		})
		return
	}

	page := params.GetPage()
	response, err := h.orbstarService.GetUserGivenOrbstars(userID, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to retrieve given orbstars",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetLeaderboard handles GET /orbstars/leaderboard
// @Summary Get company values leaderboard
// @Description Retrieve the total count of orbstars given for each company value across the entire organization
// @Tags Orbstars
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.ValueStats
// @Failure 401 {object} models.ErrorResponse
// @Router /orbstars/leaderboard [get]
func (h *OrbstarHandler) GetLeaderboard(c *gin.Context) {
	leaderboard, err := h.orbstarService.GetLeaderboard()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to retrieve leaderboard",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, leaderboard)
}

// GetValues handles GET /values
// @Summary Get company values
// @Description Retrieve the list of company values that can be used for recognition
// @Tags Values
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.ValuesResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /values [get]
func (h *OrbstarHandler) GetValues(c *gin.Context) {
	response := models.GetValuesResponse()
	c.JSON(http.StatusOK, response)
}

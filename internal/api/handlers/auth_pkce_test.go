package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"stellar-go/internal/auth"
	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestAuthHandler(t *testing.T) (*AuthHandler, *auth.OktaManager) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto-migrate
	err = db.AutoMigrate(&models.User{})
	require.NoError(t, err)

	// Setup test config
	cfg := &config.Config{
		JWT: config.JWTConfig{
			SecretKey:   "test-secret-key-12345",
			BackendURL:  "http://localhost:8080",
			FrontendURL: "http://localhost:3000",
		},
		Okta: config.OktaConfig{
			Domain:       "test.okta.com",
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			RedirectURI:  "http://localhost:8080/login/callback",
		},
	}

	// Initialize managers and services
	jwtManager := auth.NewJWTManager(cfg)
	oktaManager, err := auth.NewOktaManager(cfg)
	require.NoError(t, err)

	userService := services.NewUserService(db)
	tokenService := services.NewTokenService(db)

	// Create auth handler
	authHandler := NewAuthHandler(jwtManager, oktaManager, userService, tokenService, cfg)

	return authHandler, oktaManager
}

func TestAuthHandler_ExchangeSession_Success(t *testing.T) {
	authHandler, oktaManager := setupTestAuthHandler(t)

	// Store a test session
	accessToken := "test-access-token"
	refreshToken := "test-refresh-token"
	sessionID, err := oktaManager.StoreTokenSession(accessToken, refreshToken)
	require.NoError(t, err)

	// Create request
	reqBody := models.SessionExchangeRequest{
		SessionID: sessionID,
	}
	jsonBody, err := json.Marshal(reqBody)
	require.NoError(t, err)

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/exchange", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Call handler
	authHandler.ExchangeSession(c)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	var response models.SessionExchangeResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, accessToken, response.Access)
	assert.Equal(t, refreshToken, response.Refresh)
}

func TestAuthHandler_ExchangeSession_InvalidSession(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Create request with invalid session ID
	reqBody := models.SessionExchangeRequest{
		SessionID: "invalid-session-id",
	}
	jsonBody, err := json.Marshal(reqBody)
	require.NoError(t, err)

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/exchange", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Call handler
	authHandler.ExchangeSession(c)

	// Verify error response
	assert.Equal(t, http.StatusNotFound, w.Code)

	var errorResponse models.ErrorResponse
	err = json.Unmarshal(w.Body.Bytes(), &errorResponse)
	require.NoError(t, err)

	assert.Equal(t, "Invalid or expired session", errorResponse.Error)
	assert.Equal(t, "invalid_session", errorResponse.Code)
}

func TestAuthHandler_ExchangeSession_MissingSessionID(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Create request with empty session ID
	reqBody := models.SessionExchangeRequest{
		SessionID: "",
	}
	jsonBody, err := json.Marshal(reqBody)
	require.NoError(t, err)

	// Setup Gin context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/exchange", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Call handler
	authHandler.ExchangeSession(c)

	// Verify error response
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var errorResponse models.ErrorResponse
	err = json.Unmarshal(w.Body.Bytes(), &errorResponse)
	require.NoError(t, err)

	assert.Equal(t, "Session ID is required", errorResponse.Error)
	assert.Equal(t, "missing_session_id", errorResponse.Code)
}

func TestAuthHandler_ExchangeSession_InvalidJSON(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Setup Gin context with invalid JSON
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/exchange", bytes.NewBuffer([]byte("invalid json")))
	c.Request.Header.Set("Content-Type", "application/json")

	// Call handler
	authHandler.ExchangeSession(c)

	// Verify error response
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var errorResponse models.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
	require.NoError(t, err)

	assert.Equal(t, "Invalid request body", errorResponse.Error)
	assert.Equal(t, "invalid_request", errorResponse.Code)
}

func TestAuthHandler_HandleIDPInitiatedLogin_Success(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Create request with return_to parameter
	req, err := http.NewRequest("GET", "/idp-login?return_to=/some-page", nil)
	require.NoError(t, err)

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	gin.SetMode(gin.TestMode)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call handler
	authHandler.HandleIDPInitiatedLogin(c)

	// Check response
	assert.Equal(t, http.StatusFound, w.Code)

	// Check redirect location contains return_to parameter
	location := w.Header().Get("Location")
	assert.Contains(t, location, "/login")
	assert.Contains(t, location, "return_to=%2Fsome-page")
}

func TestAuthHandler_HandleIDPInitiatedLogin_DefaultReturnTo(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Create request without return_to parameter
	req, err := http.NewRequest("GET", "/idp-login", nil)
	require.NoError(t, err)

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	gin.SetMode(gin.TestMode)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call handler
	authHandler.HandleIDPInitiatedLogin(c)

	// Check response
	assert.Equal(t, http.StatusFound, w.Code)

	// Check redirect location contains default return_to
	location := w.Header().Get("Location")
	assert.Contains(t, location, "/login")
	assert.Contains(t, location, "return_to=%2Fdashboard")
}

func TestAuthHandler_HandleIDPInitiatedLogin_InvalidReturnTo(t *testing.T) {
	authHandler, _ := setupTestAuthHandler(t)

	// Create request with invalid return_to parameter (external URL)
	req, err := http.NewRequest("GET", "/idp-login?return_to=https://evil.com/steal-tokens", nil)
	require.NoError(t, err)

	// Create response recorder
	w := httptest.NewRecorder()

	// Create gin context
	gin.SetMode(gin.TestMode)
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call handler
	authHandler.HandleIDPInitiatedLogin(c)

	// Check response
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "invalid_return_url")
}

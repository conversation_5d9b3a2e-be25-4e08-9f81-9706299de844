package scim

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SCIMHandler handles SCIM 2.0 endpoints
type SCIMHandler struct {
	userService *services.UserService
	config      *config.Config
}

// NewSCIMHandler creates a new SCIM handler
func NewSCIMHandler(userService *services.UserService, cfg *config.Config) *SCIMHandler {
	return &SCIMHandler{
		userService: userService,
		config:      cfg,
	}
}

// SCIMUser represents a SCIM user resource
type SCIMUser struct {
	ID          string      `json:"id,omitempty"`
	Schemas     []string    `json:"schemas"`
	UserName    string      `json:"userName"`
	Name        SCIMName    `json:"name"`
	Emails      []SCIMEmail `json:"emails"`
	DisplayName string      `json:"displayName,omitempty"`
	Locale      string      `json:"locale,omitempty"`
	ExternalID  string      `json:"externalId,omitempty"`
	Active      bool        `json:"active"`
	Groups      []string    `json:"groups,omitempty"`
	Meta        SCIMMeta    `json:"meta,omitempty"`
}

// SCIMName represents the name object in SCIM
type SCIMName struct {
	GivenName  string `json:"givenName"`
	FamilyName string `json:"familyName"`
}

// SCIMEmail represents an email in SCIM
type SCIMEmail struct {
	Value   string `json:"value"`
	Primary bool   `json:"primary"`
	Type    string `json:"type,omitempty"`
	Display string `json:"display,omitempty"`
}

// SCIMMeta represents metadata in SCIM
type SCIMMeta struct {
	ResourceType string `json:"resourceType"`
	Created      string `json:"created,omitempty"`
	LastModified string `json:"lastModified,omitempty"`
	Location     string `json:"location,omitempty"`
}

// SCIMListResponse represents a SCIM list response
type SCIMListResponse struct {
	Schemas      []string   `json:"schemas"`
	TotalResults int        `json:"totalResults"`
	StartIndex   int        `json:"startIndex"`
	ItemsPerPage int        `json:"itemsPerPage"`
	Resources    []SCIMUser `json:"Resources"`
}

// SCIMError represents a SCIM error response
type SCIMError struct {
	Schemas []string `json:"schemas"`
	Detail  string   `json:"detail"`
	Status  string   `json:"status"`
}

// SCIMPatchRequest represents a SCIM PATCH request
type SCIMPatchRequest struct {
	Schemas    []string           `json:"schemas"`
	Operations []SCIMPatchOperation `json:"Operations"`
}

// SCIMPatchOperation represents a single PATCH operation
type SCIMPatchOperation struct {
	Op    string                 `json:"op"`
	Path  string                 `json:"path,omitempty"`
	Value map[string]interface{} `json:"value,omitempty"`
}

// AuthMiddleware validates the SCIM bearer token
func (h *SCIMHandler) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			h.sendSCIMError(c, http.StatusUnauthorized, "Authorization header required")
			return
		}

		var token string

		// Handle both "Bearer token" and raw token formats
		// Okta can send either format depending on configuration
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			// Standard "Bearer token" format
			token = parts[1]
		} else if len(parts) == 1 {
			// Raw token format (Okta SCIM sometimes sends this)
			token = authHeader
		} else {
			h.sendSCIMError(c, http.StatusUnauthorized, "Invalid authorization header format")
			return
		}

		if token != h.config.SCIM.BearerToken {
			h.sendSCIMError(c, http.StatusUnauthorized, "Invalid bearer token")
			return
		}

		c.Next()
	}
}

// GetUsers handles GET /scim/v2/Users
// @Summary List users via SCIM
// @Description Retrieve users using SCIM 2.0 protocol with optional filtering
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param startIndex query int false "Start index for pagination" default(1)
// @Param count query int false "Number of results per page" default(100)
// @Param filter query string false "SCIM filter expression (supports userName eq)"
// @Success 200 {object} SCIMListResponse
// @Failure 401 {object} SCIMError
// @Router /scim/v2/Users [get]
func (h *SCIMHandler) GetUsers(c *gin.Context) {
	startIndex, _ := strconv.Atoi(c.DefaultQuery("startIndex", "1"))
	count, _ := strconv.Atoi(c.DefaultQuery("count", "100"))
	filter := c.Query("filter")

	if startIndex < 1 {
		startIndex = 1
	}
	if count < 1 || count > 100 {
		count = 100
	}

	// Parse SCIM filter for userName eq "email"
	var emailFilter string
	if filter != "" {
		emailFilter = h.parseSCIMFilter(filter)
	}

	users, total, err := h.userService.GetUsersForSCIM(startIndex-1, count, emailFilter)
	if err != nil {
		h.sendSCIMError(c, http.StatusInternalServerError, "Failed to retrieve users")
		return
	}

	scimUsers := make([]SCIMUser, len(users))
	for i, user := range users {
		scimUsers[i] = h.userToSCIM(&user)
	}

	response := SCIMListResponse{
		Schemas:      []string{"urn:ietf:params:scim:api:messages:2.0:ListResponse"},
		TotalResults: total,
		StartIndex:   startIndex,
		ItemsPerPage: len(scimUsers),
		Resources:    scimUsers,
	}

	c.JSON(http.StatusOK, response)
}

// GetUser handles GET /scim/v2/Users/<USER>
// @Summary Get user by ID via SCIM
// @Description Retrieve a specific user using SCIM 2.0 protocol
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} SCIMUser
// @Failure 401 {object} SCIMError
// @Failure 404 {object} SCIMError
// @Router /scim/v2/Users/<USER>
func (h *SCIMHandler) GetUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid user ID")
		return
	}

	user, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.sendSCIMError(c, http.StatusNotFound, "User not found")
		} else {
			h.sendSCIMError(c, http.StatusInternalServerError, "Failed to retrieve user")
		}
		return
	}

	scimUser := h.userToSCIM(user)
	c.JSON(http.StatusOK, scimUser)
}

// CreateUser handles POST /scim/v2/Users
// @Summary Create user via SCIM
// @Description Create a new user using SCIM 2.0 protocol
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user body SCIMUser true "User data"
// @Success 201 {object} SCIMUser
// @Failure 400 {object} SCIMError
// @Failure 401 {object} SCIMError
// @Failure 409 {object} SCIMError
// @Router /scim/v2/Users [post]
func (h *SCIMHandler) CreateUser(c *gin.Context) {
	var scimUser SCIMUser
	if err := c.ShouldBindJSON(&scimUser); err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate required fields
	if scimUser.UserName == "" || len(scimUser.Emails) == 0 {
		h.sendSCIMError(c, http.StatusBadRequest, "userName and emails are required")
		return
	}

	// Create user model
	user := models.User{
		Email:     scimUser.Emails[0].Value,
		FirstName: scimUser.Name.GivenName,
		LastName:  scimUser.Name.FamilyName,
		Active:    scimUser.Active, // Set authentication and visibility status
		Admin:     false,           // Default to non-admin
	}

	createdUser, err := h.userService.CreateUser(&user)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			h.sendSCIMError(c, http.StatusConflict, "User already exists")
		} else {
			h.sendSCIMError(c, http.StatusInternalServerError, "Failed to create user")
		}
		return
	}

	responseUser := h.userToSCIM(createdUser)
	c.JSON(http.StatusCreated, responseUser)
}

// UpdateUser handles PUT /scim/v2/Users/<USER>
// @Summary Update user via SCIM
// @Description Update an existing user using SCIM 2.0 protocol
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param user body SCIMUser true "User data"
// @Success 200 {object} SCIMUser
// @Failure 400 {object} SCIMError
// @Failure 401 {object} SCIMError
// @Failure 404 {object} SCIMError
// @Router /scim/v2/Users/<USER>
func (h *SCIMHandler) UpdateUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid user ID")
		return
	}

	var scimUser SCIMUser
	if err := c.ShouldBindJSON(&scimUser); err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Get existing user
	existingUser, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.sendSCIMError(c, http.StatusNotFound, "User not found")
		} else {
			h.sendSCIMError(c, http.StatusInternalServerError, "Failed to retrieve user")
		}
		return
	}

	// Update user fields
	existingUser.FirstName = scimUser.Name.GivenName
	existingUser.LastName = scimUser.Name.FamilyName
	existingUser.Active = scimUser.Active // Update authentication and visibility status

	updatedUser, err := h.userService.UpdateUser(existingUser)
	if err != nil {
		h.sendSCIMError(c, http.StatusInternalServerError, "Failed to update user")
		return
	}

	responseUser := h.userToSCIM(updatedUser)
	c.JSON(http.StatusOK, responseUser)
}

// PatchUser handles PATCH /scim/v2/Users/<USER>
// @Summary Patch user via SCIM
// @Description Update specific user attributes using SCIM 2.0 PATCH protocol
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param patch body SCIMPatchRequest true "PATCH operations"
// @Success 200 {object} SCIMUser
// @Failure 400 {object} SCIMError
// @Failure 401 {object} SCIMError
// @Failure 404 {object} SCIMError
// @Router /scim/v2/Users/<USER>
func (h *SCIMHandler) PatchUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid user ID")
		return
	}

	var patchRequest SCIMPatchRequest
	if err := c.ShouldBindJSON(&patchRequest); err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Get existing user
	existingUser, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.sendSCIMError(c, http.StatusNotFound, "User not found")
		} else {
			h.sendSCIMError(c, http.StatusInternalServerError, "Failed to retrieve user")
		}
		return
	}

	// Apply PATCH operations
	for _, operation := range patchRequest.Operations {
		switch operation.Op {
		case "replace":
			if operation.Value != nil {
				if active, ok := operation.Value["active"].(bool); ok {
					existingUser.Active = active // Update authentication and visibility status
				}
				if givenName, ok := operation.Value["name"].(map[string]interface{}); ok {
					if gn, ok := givenName["givenName"].(string); ok {
						existingUser.FirstName = gn
					}
					if fn, ok := givenName["familyName"].(string); ok {
						existingUser.LastName = fn
					}
				}
			}
		}
	}

	updatedUser, err := h.userService.UpdateUser(existingUser)
	if err != nil {
		h.sendSCIMError(c, http.StatusInternalServerError, "Failed to update user")
		return
	}

	responseUser := h.userToSCIM(updatedUser)
	c.JSON(http.StatusOK, responseUser)
}

// DeleteUser handles DELETE /scim/v2/Users/<USER>
// @Summary Delete user via SCIM
// @Description Delete a user using SCIM 2.0 protocol
// @Tags SCIM
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 204
// @Failure 401 {object} SCIMError
// @Failure 404 {object} SCIMError
// @Router /scim/v2/Users/<USER>
func (h *SCIMHandler) DeleteUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		h.sendSCIMError(c, http.StatusBadRequest, "Invalid user ID")
		return
	}

	err = h.userService.DeleteUser(uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.sendSCIMError(c, http.StatusNotFound, "User not found")
		} else {
			h.sendSCIMError(c, http.StatusInternalServerError, "Failed to delete user")
		}
		return
	}

	c.Status(http.StatusNoContent)
}

// userToSCIM converts a User model to SCIM format
func (h *SCIMHandler) userToSCIM(user *models.User) SCIMUser {
	return SCIMUser{
		ID:          fmt.Sprintf("%d", user.ID),
		Schemas:     []string{"urn:ietf:params:scim:schemas:core:2.0:User"},
		UserName:    user.Email,
		DisplayName: user.FirstName + " " + user.LastName,
		Locale:      "en-US",
		ExternalID:  fmt.Sprintf("%d", user.ID), // Use internal ID as externalId
		Active:      user.Active,                // Use active status for SCIM
		Name: SCIMName{
			GivenName:  user.FirstName,
			FamilyName: user.LastName,
		},
		Emails: []SCIMEmail{
			{
				Value:   user.Email,
				Primary: true,
				Type:    "work",
				Display: user.Email,
			},
		},
		Groups: []string{}, // Empty groups array as required by Okta
		Meta: SCIMMeta{
			ResourceType: "User",
			Location:     fmt.Sprintf("/scim/v2/Users/<USER>", user.ID),
		},
	}
}

// parseSCIMFilter parses a basic SCIM filter expression
// Currently supports: userName eq "<EMAIL>"
func (h *SCIMHandler) parseSCIMFilter(filter string) string {
	// Simple parser for userName eq "email" pattern
	// Example: userName eq "<EMAIL>"

	// Remove extra spaces and normalize
	filter = strings.TrimSpace(filter)

	// Look for userName eq pattern
	if strings.Contains(strings.ToLower(filter), "username eq") {
		// Find the quoted email value
		parts := strings.Split(filter, "\"")
		if len(parts) >= 2 {
			return parts[1] // Return the email between quotes
		}
	}

	return "" // Return empty if filter not supported
}

// sendSCIMError sends a SCIM-formatted error response
func (h *SCIMHandler) sendSCIMError(c *gin.Context, status int, detail string) {
	error := SCIMError{
		Schemas: []string{"urn:ietf:params:scim:api:messages:2.0:Error"},
		Detail:  detail,
		Status:  fmt.Sprintf("%d", status),
	}
	c.JSON(status, error)
}

# Vue3 Frontend Integration with Okta PKCE Authentication

This document provides comprehensive guidance for integrating a Vue3 frontend application with the Stellar Go API's Okta-based authentication system using PKCE (Proof Key for Code Exchange) flow.

## Table of Contents

1. [Authentication Flow Overview](#authentication-flow-overview)
2. [Backend API Endpoints](#backend-api-endpoints)
3. [Vue3 Integration Setup](#vue3-integration-setup)
4. [Token Management](#token-management)
5. [Error Handling](#error-handling)
6. [Security Considerations](#security-considerations)
7. [Example Implementation](#example-implementation)

## Authentication Flow Overview

The authentication system uses Okta OIDC (OpenID Connect) with PKCE (Proof Key for Code Exchange) for secure user authentication and JWT tokens for API authorization. PKCE provides enhanced security compared to the implicit flow by eliminating token exposure in URLs.

### High-Level Architecture (PKCE Flow)

```mermaid
graph TB
    A[Vue3 Frontend] -->|1. Initiate Login| B[Backend /login]
    B -->|2. Generate PKCE Parameters| B
    B -->|3. Redirect with code_challenge| C[Okta Authorization Server]
    C -->|4. User Authentication| D[User Login Form]
    D -->|5. Authorization Code| E[Backend /login/callback]
    E -->|6. Exchange Code + code_verifier| C
    C -->|7. Return Okta Tokens| E
    E -->|8. Generate JWT Tokens| F[JWT Manager]
    E -->|9. Store in Temporary Session| G[Session Storage]
    E -->|10. Redirect with Session ID| A
    A -->|11. Call /auth/exchange| H[Backend /auth/exchange]
    H -->|12. Return JWT Tokens| A
    A -->|13. Store Tokens| I[Local Storage]
    A -->|14. API Requests with Bearer Token| J[Protected API Endpoints]
    J -->|15. Validate JWT| K[Auth Middleware]
    K -->|16. Return User Data| A
```

### Detailed PKCE Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant V as Vue3 App
    participant B as Backend API
    participant S as Session Storage
    participant O as Okta
    participant DB as Database

    U->>V: Click Login
    V->>B: GET /login
    B->>B: Generate PKCE code_verifier & code_challenge
    B->>S: Store code_verifier with state
    B->>O: Generate Auth URL with code_challenge
    B->>V: Redirect to Okta
    V->>O: User Authentication
    O->>U: Login Form
    U->>O: Enter Credentials
    O->>B: Callback with Auth Code
    B->>S: Retrieve code_verifier using state
    B->>O: Exchange Code + code_verifier for Tokens
    O->>B: Return Access Token & ID Token
    B->>O: Get User Info
    O->>B: Return User Profile
    B->>DB: Create/Update User
    DB->>B: User Record
    B->>B: Generate JWT Tokens
    B->>S: Store JWT Tokens in temporary session
    B->>V: Redirect with Session ID only
    V->>B: POST /auth/exchange with Session ID
    B->>S: Retrieve & Delete JWT Tokens
    B->>V: Return JWT Tokens in JSON
    V->>V: Store Tokens in localStorage
    V->>B: API Request with Bearer Token
    B->>B: Validate JWT
    B->>V: Return Protected Data
```

## Backend API Endpoints

### Authentication Endpoints

#### 1. Initiate Login
```http
GET /login
```
- **Purpose**: Redirects user to Okta for authentication
- **Response**: HTTP 302 redirect to Okta authorization URL
- **Frontend Action**: Navigate user to this endpoint

#### 2. Login Callback
```http
GET /login/callback?code={auth_code}&state={state}
```
- **Purpose**: Handles Okta callback using PKCE and creates temporary session
- **Parameters**:
  - `code`: Authorization code from Okta
  - `state`: CSRF protection parameter (used to retrieve code_verifier)
- **Response**: HTTP 302 redirect to frontend with session ID only
- **Redirect Format**: `{FRONTEND_URL}?session={session_id}`

#### 3. Session Exchange (New)
```http
POST /auth/exchange
Content-Type: application/json

{
  "session_id": "abc123def456ghi789"
}
```
- **Purpose**: Exchange temporary session ID for JWT tokens
- **Response**:
```json
{
  "access": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."
}
```
- **Security**: Session expires in 5 minutes and can only be used once

#### 4. Refresh Tokens
```http
POST /auth/refresh
Content-Type: application/json

{
  "access": "current_access_token",
  "refresh": "current_refresh_token"
}
```
- **Purpose**: Exchange refresh token for new token pair
- **Response**:
```json
{
  "access": "new_access_token",
  "refresh": "new_refresh_token"
}
```

### Protected Endpoints

All protected endpoints require the `Authorization` header:
```http
Authorization: Bearer {access_token}
```

#### User Information
```http
GET /user
```
- **Purpose**: Get current user profile
- **Response**:
```json
{
  "id": 123,
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "show": true,
  "admin": false
}
```

## Vue3 Integration Setup

### 1. Install Dependencies

```bash
npm install axios vue-router@4 pinia
```

### 2. Environment Configuration

Create `.env` file:
```env
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_NAME=Stellar App
```

### 3. API Client Setup

Create `src/services/api.js`:
```javascript
import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.accessToken) {
      config.headers.Authorization = `Bearer ${authStore.accessToken}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const authStore = useAuthStore()
    
    if (error.response?.status === 401 && authStore.refreshToken) {
      try {
        await authStore.refreshTokens()
        // Retry the original request
        return api.request(error.config)
      } catch (refreshError) {
        authStore.logout()
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

export default api
```

### 4. Authentication Store (Pinia)

Create `src/stores/auth.js`:
```javascript
import { defineStore } from 'pinia'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    accessToken: localStorage.getItem('access_token'),
    refreshToken: localStorage.getItem('refresh_token'),
    user: null,
    isLoading: false,
  }),

  getters: {
    isAuthenticated: (state) => !!state.accessToken,
    isAdmin: (state) => state.user?.admin || false,
  },

  actions: {
    // Initialize auth from URL parameters (after Okta callback with PKCE)
    async initializeFromCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const sessionId = urlParams.get('session')

      if (sessionId) {
        try {
          // Exchange session ID for JWT tokens
          const response = await api.post('/auth/exchange', {
            session_id: sessionId
          })

          this.setTokens(response.data.access, response.data.refresh)

          // Clean URL
          window.history.replaceState({}, document.title, window.location.pathname)
          return true
        } catch (error) {
          console.error('Failed to exchange session for tokens:', error)
          // Clean URL even on error
          window.history.replaceState({}, document.title, window.location.pathname)
          return false
        }
      }
      return false
    },

    // Set tokens and store them
    setTokens(accessToken, refreshToken) {
      this.accessToken = accessToken
      this.refreshToken = refreshToken
      localStorage.setItem('access_token', accessToken)
      localStorage.setItem('refresh_token', refreshToken)
    },

    // Login - redirect to backend login endpoint
    login() {
      window.location.href = `${import.meta.env.VITE_API_BASE_URL}/login`
    },

    // Logout
    logout() {
      this.accessToken = null
      this.refreshToken = null
      this.user = null
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    },

    // Refresh tokens
    async refreshTokens() {
      if (!this.refreshToken) {
        throw new Error('No refresh token available')
      }

      try {
        const response = await api.post('/auth/refresh', {
          access: this.accessToken,
          refresh: this.refreshToken,
        })

        this.setTokens(response.data.access, response.data.refresh)
        return response.data
      } catch (error) {
        this.logout()
        throw error
      }
    },

    // Fetch current user
    async fetchUser() {
      if (!this.isAuthenticated) return null

      try {
        this.isLoading = true
        const response = await api.get('/user')
        this.user = response.data
        return this.user
      } catch (error) {
        console.error('Failed to fetch user:', error)
        if (error.response?.status === 401) {
          this.logout()
        }
        throw error
      } finally {
        this.isLoading = false
      }
    },
  },
})
```

## Token Management

### JWT Token Structure

The backend generates two types of JWT tokens:

1. **Access Token** (2 minutes expiry)
   - Used for API authentication
   - Short-lived for security
   - Contains user ID and type

2. **Refresh Token** (2 hours expiry)
   - Used to obtain new access tokens
   - Longer-lived but still expires
   - Must be stored securely

### Token Claims

```json
{
  "sub": "123",           // User ID
  "typ": "access",        // Token type
  "iss": "backend_url",   // Issuer
  "aud": ["frontend_url"], // Audience
  "exp": 1234567890,      // Expiration
  "iat": 1234567890,      // Issued at
  "nbf": 1234567890       // Not before
}
```

### Automatic Token Refresh

The API client automatically handles token refresh:

1. **Request Interceptor**: Adds Bearer token to all requests
2. **Response Interceptor**: Detects 401 errors and attempts refresh
3. **Retry Logic**: Retries original request with new token
4. **Fallback**: Logs out user if refresh fails

## Error Handling

### Common Error Responses

#### Authentication Errors
```json
{
  "error": "Authorization header required",
  "code": "missing_auth_header"
}
```

#### Token Errors
```json
{
  "error": "Invalid or expired token",
  "code": "invalid_token"
}
```

#### Refresh Token Errors
```json
{
  "error": "Invalid refresh token",
  "code": "invalid_refresh_token"
}
```

### Error Handling in Vue Components

```javascript
// In your Vue component
import { useAuthStore } from '@/stores/auth'

export default {
  setup() {
    const authStore = useAuthStore()

    const handleApiError = (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        authStore.logout()
        router.push('/login')
      } else if (error.response?.status === 403) {
        // Insufficient permissions
        console.error('Access denied')
      } else {
        // Other errors
        console.error('API Error:', error.response?.data?.error)
      }
    }

    return { handleApiError }
  }
}
```

## Security Considerations

### PKCE vs Implicit Flow Security Benefits

The new PKCE (Proof Key for Code Exchange) implementation provides significant security improvements over the previous implicit flow:

#### Security Enhancements:
1. **No Token Exposure in URLs**: JWT tokens are never exposed in browser URLs or redirect URIs
2. **Code Verifier Protection**: The `code_verifier` is generated and stored securely on the backend
3. **One-Time Session Usage**: Temporary sessions can only be used once and expire quickly (5 minutes)
4. **Reduced Attack Surface**: Eliminates risks from URL-based token interception
5. **CSRF Protection**: State parameter validation prevents cross-site request forgery
6. **Session Isolation**: Each authentication flow uses a unique session with automatic cleanup

#### Flow Comparison:
- **Implicit Flow**: `GET /callback?access_token=...&refresh_token=...` (tokens in URL)
- **PKCE Flow**: `GET /callback?session=abc123` → `POST /auth/exchange` (tokens in secure JSON response)

### 1. Token Storage

**Recommended Approach**: Use `localStorage` for tokens with proper security measures:

```javascript
// Secure token storage utility
class SecureStorage {
  static setItem(key, value) {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.error('Failed to store token:', error)
    }
  }

  static getItem(key) {
    try {
      return localStorage.getItem(key)
    } catch (error) {
      console.error('Failed to retrieve token:', error)
      return null
    }
  }

  static removeItem(key) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove token:', error)
    }
  }
}
```

### 2. HTTPS Requirements

- **Production**: Always use HTTPS for both frontend and backend
- **Development**: Use HTTP only for local development
- **Okta Configuration**: Ensure redirect URIs match your environment

### 3. CORS Configuration

The backend includes CORS middleware. Ensure your frontend domain is properly configured:

```javascript
// Backend CORS configuration (already implemented)
router.Use(middleware.CORSMiddleware())
```

### 4. State Parameter Validation

The backend generates a `state` parameter for CSRF protection. While the current implementation doesn't validate it (noted in code), production should implement proper state validation.

### 5. Token Expiration Handling

- **Access tokens**: 2-minute expiry (very short for security)
- **Refresh tokens**: 2-hour expiry
- **Automatic refresh**: Implemented in API client
- **Graceful degradation**: Logout on refresh failure

## Example Implementation

### 1. Main App Setup

Create `src/main.js`:
```javascript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import { useAuthStore } from './stores/auth'

// Import your components
import Home from './views/Home.vue'
import Login from './views/Login.vue'
import Dashboard from './views/Dashboard.vue'

// Router configuration
const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/login', name: 'Login', component: Login },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Handle Okta callback with PKCE session exchange
  if (to.query.session) {
    const success = await authStore.initializeFromCallback()
    if (success) {
      next('/dashboard')
    } else {
      next('/login')
    }
    return
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // Fetch user data if authenticated but no user data
  if (authStore.isAuthenticated && !authStore.user) {
    try {
      await authStore.fetchUser()
    } catch (error) {
      console.error('Failed to fetch user:', error)
      next('/login')
      return
    }
  }

  next()
})

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.mount('#app')
```

### 2. Login Component

Create `src/views/Login.vue`:
```vue
<template>
  <div class="login-container">
    <div class="login-card">
      <h1>Welcome to Stellar App</h1>
      <p>Please sign in with your company account</p>

      <button
        @click="handleLogin"
        :disabled="isLoading"
        class="login-button"
      >
        <span v-if="isLoading">Signing in...</span>
        <span v-else>Sign in with Okta</span>
      </button>

      <div v-if="error" class="error-message">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

export default {
  name: 'Login',
  setup() {
    const authStore = useAuthStore()
    const router = useRouter()
    const isLoading = ref(false)
    const error = ref('')

    // Redirect if already authenticated
    if (authStore.isAuthenticated) {
      router.push('/dashboard')
    }

    const handleLogin = () => {
      try {
        isLoading.value = true
        error.value = ''
        authStore.login() // This will redirect to backend /login
      } catch (err) {
        error.value = 'Failed to initiate login'
        isLoading.value = false
      }
    }

    return {
      handleLogin,
      isLoading,
      error,
    }
  },
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.login-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 1rem;
  width: 100%;
}

.login-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #f8d7da;
  border-radius: 4px;
}
</style>
```

### 3. Dashboard Component

Create `src/views/Dashboard.vue`:
```vue
<template>
  <div class="dashboard">
    <header class="dashboard-header">
      <h1>Dashboard</h1>
      <div class="user-info">
        <span v-if="authStore.user">
          Welcome, {{ authStore.user.first_name }} {{ authStore.user.last_name }}
          <span v-if="authStore.user.admin" class="admin-badge">Admin</span>
        </span>
        <button @click="handleLogout" class="logout-button">
          Logout
        </button>
      </div>
    </header>

    <main class="dashboard-content">
      <div v-if="authStore.isLoading" class="loading">
        Loading user data...
      </div>

      <div v-else-if="authStore.user" class="user-profile">
        <h2>Your Profile</h2>
        <div class="profile-info">
          <p><strong>Email:</strong> {{ authStore.user.email }}</p>
          <p><strong>Name:</strong> {{ authStore.user.first_name }} {{ authStore.user.last_name }}</p>
          <p><strong>Visible:</strong> {{ authStore.user.show ? 'Yes' : 'No' }}</p>
          <p><strong>Admin:</strong> {{ authStore.user.admin ? 'Yes' : 'No' }}</p>
        </div>
      </div>

      <!-- Example of making API calls -->
      <div class="api-example">
        <h2>API Example</h2>
        <button @click="fetchUsers" :disabled="loadingUsers">
          {{ loadingUsers ? 'Loading...' : 'Fetch Users' }}
        </button>

        <div v-if="users.length > 0" class="users-list">
          <h3>Users ({{ users.length }})</h3>
          <ul>
            <li v-for="user in users" :key="user.id">
              {{ user.first_name }} {{ user.last_name }} ({{ user.email }})
            </li>
          </ul>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import api from '@/services/api'

export default {
  name: 'Dashboard',
  setup() {
    const authStore = useAuthStore()
    const router = useRouter()
    const users = ref([])
    const loadingUsers = ref(false)

    const handleLogout = () => {
      authStore.logout()
      router.push('/login')
    }

    const fetchUsers = async () => {
      try {
        loadingUsers.value = true
        const response = await api.get('/users')
        users.value = response.data
      } catch (error) {
        console.error('Failed to fetch users:', error)
        // Error handling is done by the API interceptor
      } finally {
        loadingUsers.value = false
      }
    }

    return {
      authStore,
      users,
      loadingUsers,
      handleLogout,
      fetchUsers,
    }
  },
}
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.dashboard-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-badge {
  background-color: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 0.5rem;
}

.logout-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.logout-button:hover {
  background-color: #c82333;
}

.dashboard-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.user-profile, .api-example {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.profile-info p {
  margin: 0.5rem 0;
}

.users-list {
  margin-top: 1rem;
}

.users-list ul {
  list-style: none;
  padding: 0;
}

.users-list li {
  padding: 0.5rem;
  border-bottom: 1px solid #eee;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}
</style>
```

## Testing the Integration

### 1. Environment Setup

Ensure your backend is running with proper environment variables:

```bash
# Backend .env
OKTA_DOMAIN=your-domain.okta.com
OKTA_CLIENT_ID=your-client-id
OKTA_CLIENT_SECRET=your-client-secret
OKTA_REDIRECT_URI=http://localhost:8080/login/callback
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8080
```

### 2. Frontend Development Server

```bash
npm run dev
```

### 3. Test Flow

1. Navigate to `http://localhost:3000`
2. Click "Sign in with Okta"
3. Complete Okta authentication
4. Verify redirect back to frontend with tokens
5. Check that user data is loaded
6. Test API calls with authentication
7. Test token refresh (wait for access token to expire)
8. Test logout functionality

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend CORS is configured for your frontend domain
2. **Redirect URI Mismatch**: Verify Okta app configuration matches backend
3. **Token Storage**: Check browser developer tools for localStorage entries
4. **API Errors**: Monitor network tab for failed requests and error responses

### Debug Mode

Add debug logging to your auth store:

```javascript
// In auth store actions
console.log('Auth Debug:', {
  accessToken: !!this.accessToken,
  refreshToken: !!this.refreshToken,
  user: this.user,
  isAuthenticated: this.isAuthenticated,
})
```

This completes the comprehensive Vue3 integration guide for the Stellar Go API's Okta authentication system.
```

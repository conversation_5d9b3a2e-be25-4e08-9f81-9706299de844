-- Add active column to userprofile table for authentication status
ALTER TABLE userprofile ADD COLUMN active BOOLEAN DEFAULT TRUE NOT NULL;

-- Create index for active column for performance
CREATE INDEX idx_userprofile_active ON userprofile(active);

-- Create refresh_token_blacklist table for invalidated tokens
CREATE TABLE refresh_token_blacklist (
    id SERIAL PRIMARY KEY,
    token_id VARCHAR(255) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES userprofile(id) ON DELETE CASCADE,
    blacklisted_at TIMESTAMP DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_refresh_token_blacklist_token_id ON refresh_token_blacklist(token_id);
CREATE INDEX idx_refresh_token_blacklist_user_id ON refresh_token_blacklist(user_id);
CREATE INDEX idx_refresh_token_blacklist_expires_at ON refresh_token_blacklist(expires_at);

-- Update existing users to be active by default
UPDATE userprofile SET active = TRUE WHERE active IS NULL;
